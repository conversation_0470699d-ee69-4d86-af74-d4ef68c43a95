```
// do long
{
  "channel": "push.personal.position",
  "data": {
    "adlLevel": 0,
    "autoAddIm": False,
    "closeAvgPrice": 0,
    "closeProfitLoss": 0,
    "closeVol": 0,
    "createTime": 1755352562133,
    "deductFeeList": [],
    "fee": 0,
    "frozenVol": 0,
    "holdAvgPrice": 3.7159,
    "holdAvgPriceFullyScale": "3.7159",
    "holdFee": 0,
    "holdVol": 1,
    "im": 0.037159,
    "leverage": 100,
    "liquidatePrice": 0,
    "makerFeeRate": 0,
    "marginRatio": 0.0001,
    "newCloseAvgPrice": 0,
    "newOpenAvgPrice": 3.7159,
    "oim": 0.037159,
    "openAvgPrice": 3.7159,
    "openAvgPriceFullyScale": "3.7159",
    "openType": 2,
    "pnl": 0.5188,
    "positionId": 990933345,
    "positionType": 1,
    "realised": 0,
    "state": 1,
    "symbol": "SUI_USDT",
    "takerFeeRate": 0,
    "updateTime": 1755352562133,
    "version": 1
  },
  "ts": 1755352562191
}
// close long
{
  "channel": "push.personal.position",
  "data": {
    "autoAddIm": False,
    "closeAvgPrice": 3.7176,
    "closeProfitLoss": 0.0017,
    "closeVol": 1,
    "createTime": 1755352562133,
    "deductFeeList": [],
    "fee": 0,
    "frozenVol": 0,
    "holdAvgPrice": 3.7159,
    "holdAvgPriceFullyScale": "3.7159",
    "holdFee": 0,
    "holdVol": 0,
    "im": 0,
    "leverage": 100,
    "liquidatePrice": 0,
    "makerFeeRate": 0,
    "marginRatio": 0.0001,
    "newCloseAvgPrice": 3.7176,
    "newOpenAvgPrice": 3.7159,
    "oim": 0,
    "openAvgPrice": 3.7159,
    "openAvgPriceFullyScale": "3.7159",
    "openType": 2,
    "pnl": 0,
    "positionId": 990933345,
    "positionType": 1,
    "realised": 0.0017,
    "state": 3,
    "symbol": "SUI_USDT",
    "takerFeeRate": 0,
    "updateTime": 1755352774562,
    "version": 3
  },
  "ts": 1755352774624
}

// open short
{
  "channel": "push.personal.position",
  "data": {
    "adlLevel": 0,
    "autoAddIm": False,
    "closeAvgPrice": 0,
    "closeProfitLoss": 0,
    "closeVol": 0,
    "createTime": 1755352898462,
    "deductFeeList": [],
    "fee": 0,
    "frozenVol": 0,
    "holdAvgPrice": 3.7232,
    "holdAvgPriceFullyScale": "3.7232",
    "holdFee": 0,
    "holdVol": 1,
    "im": 0.037232,
    "leverage": 100,
    "liquidatePrice": 73.3089,
    "makerFeeRate": 0,
    "marginRatio": 0.0001,
    "newCloseAvgPrice": 0,
    "newOpenAvgPrice": 3.7232,
    "oim": 0.037232,
    "openAvgPrice": 3.7232,
    "openAvgPriceFullyScale": "3.7232",
    "openType": 2,
    "pnl": -0.5115,
    "positionId": 990938013,
    "positionType": 2,
    "realised": 0,
    "state": 1,
    "symbol": "SUI_USDT",
    "takerFeeRate": 0,
    "updateTime": 1755352898462,
    "version": 1
  },
  "ts": 1755352898536
}

// close short
{
  "channel": "push.personal.position",
  "data": {
    "autoAddIm": False,
    "closeAvgPrice": 3.7243,
    "closeProfitLoss": -0.0011,
    "closeVol": 1,
    "createTime": 1755352898462,
    "deductFeeList": [],
    "fee": 0,
    "frozenVol": 0,
    "holdAvgPrice": 3.7232,
    "holdAvgPriceFullyScale": "3.7232",
    "holdFee": 0,
    "holdVol": 0,
    "im": 0,
    "leverage": 100,
    "liquidatePrice": 73.3089,
    "makerFeeRate": 0,
    "marginRatio": 0.0001,
    "newCloseAvgPrice": 3.7243,
    "newOpenAvgPrice": 3.7232,
    "oim": 0,
    "openAvgPrice": 3.7232,
    "openAvgPriceFullyScale": "3.7232",
    "openType": 2,
    "pnl": 0,
    "positionId": 990938013,
    "positionType": 2,
    "realised": -0.0011,
    "state": 3,
    "symbol": "SUI_USDT",
    "takerFeeRate": 0,
    "updateTime": 1755352944870,
    "version": 3
  },
  "ts": 1755352944948
}
```

help implement `push.personal.position` websocket parse, just add one additional logic branch for it. we can already get it.
please note: we should align with QuantConnect Lean logic. for long position, the position should be positive number.
for short position, the position should be negative number.

please refer Binance brokerage code, how does it handle position?

it will query position HTTP REST API to get initial position, right?

here is `https://futures.greentreeone.com/api/v1/private/position/open_positions` to get open position for MEXC, it's a GET method.
and here is the response which is Long position:
```
{
  "success": true,
  "code": 0,
  "data": [
    {
      "positionId": 990947058,
      "symbol": "SUI_USDT",
      "positionType": 1,
      "openType": 2,
      "state": 1,
      "holdVol": 1,
      "frozenVol": 0,
      "closeVol": 0,
      "holdAvgPrice": 3.7226,
      "holdAvgPriceFullyScale": "3.7226",
      "openAvgPrice": 3.7226,
      "openAvgPriceFullyScale": "3.7226",
      "closeAvgPrice": 0,
      "liquidatePrice": 0,
      "oim": 0.037226,
      "im": 0.037226,
      "holdFee": 0,
      "realised": 0,
      "leverage": 100,
      "marginRatio": 0.0001,
      "createTime": 1755353502694,
      "updateTime": 1755353502694,
      "autoAddIm": false,
      "version": 1,
      "profitRatio": 0,
      "newOpenAvgPrice": 3.7226,
      "newCloseAvgPrice": 0,
      "closeProfitLoss": 0,
      "fee": 0,
      "deductFeeList": [],
      "totalFee": 0,
      "zeroSaveTotalFeeBinance": 0.002,
      "zeroTradeTotalFeeBinance": 0.002
    }
  ]
}
```

and here is the JSON response of Short position:
```
{
  "success": true,
  "code": 0,
  "data": [
    {
      "positionId": 990950714,
      "symbol": "SUI_USDT",
      "positionType": 2,
      "openType": 2,
      "state": 1,
      "holdVol": 1,
      "frozenVol": 0,
      "closeVol": 0,
      "holdAvgPrice": 3.7202,
      "holdAvgPriceFullyScale": "3.7202",
      "openAvgPrice": 3.7202,
      "openAvgPriceFullyScale": "3.7202",
      "closeAvgPrice": 0,
      "liquidatePrice": 73.3008,
      "oim": 0.037202,
      "im": 0.037202,
      "holdFee": 0,
      "realised": 0,
      "leverage": 100,
      "marginRatio": 0.0001,
      "createTime": 1755353746052,
      "updateTime": 1755353746052,
      "autoAddIm": false,
      "version": 1,
      "profitRatio": 0,
      "newOpenAvgPrice": 3.7202,
      "newCloseAvgPrice": 0,
      "closeProfitLoss": 0,
      "fee": 0,
      "deductFeeList": [],
      "totalFee": 0,
      "zeroSaveTotalFeeBinance": 0.002,
      "zeroTradeTotalFeeBinance": 0.002
    }
  ]
}
```

help implement Binan

use ```dotnet build "D:\work\xstarwalker168\Python\Finance\QuantConnectLean\TradingSolution\Lean\QuantConnect.Lean.sln" --configuration Debug ``` to check any compiling errors

use ```cd "D:\work\xstarwalker168\Python\Finance\QuantConnectLean\TradingSolution\Lean\Launcher\bin\Debug; .\QuantConnect.Lean.Launcher.exe --config D:\work\xstarwalker168\Python\Finance\QuantConnectLean\trading-bot-config.json --parameters tradeMode:2,symbol:SUI,currency:USDT,leverage:5 --results-destination-folder "D:/work/xstarwalker168/Python/Finance/QuantConnectLean/QC-Log-Dir" --algorithm-language CSharp --environment live-mexc --algorithm-location cCryptoBot.dll --data-folder "D:/work/xstarwalker168/Python/Finance/QuantConnectLean/Data"
